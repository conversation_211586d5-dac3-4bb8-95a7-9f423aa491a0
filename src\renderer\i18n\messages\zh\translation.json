{"chat": {"input-label": "输入消息", "file-not-found": "文件不存在", "file-preview-failed": "文件预览失败", "input-placeholder": "在这里输入消息，按Shift+Enter换行", "collected": "收藏", "send-failed": "发送消息失败", "tool-bar": {"attach-file": "上传附件(文件不能超过10MB)", "online-search": "联网搜索", "thinking": "推理", "disabled": "仅302.AI供应商的模型可用"}, "model-select-label": "选择模型", "model-select-placeholder": "请选择一个模型...", "model-select": "设置模型", "tab-title": "设置", "model-search-placeholder": "搜索...", "no-models-found": "没有找到匹配的模型...", "lack-model": "请先选择一个模型！", "lack-provider": "缺少模型提供商！", "edit-message": "编辑消息", "edit-message-only-save": "仅保存", "function_call": "工具", "reasoning": "推理", "vision": "照片", "file": "文件", "music": "音频", "video": "视频", "support": "支持"}, "settings": {"about-settings": {"name": "关于", "version": "版本", "description": {"title": "应用描述", "content": "302 AI Studio 是一个强大的AI聊天应用，为用户提供智能对话体验。支持多种AI模型，具备文件上传、联网搜索等功能，致力于为用户打造最佳的AI交互平台。"}, "help-center": {"title": "帮助中心"}, "terms-of-service": {"title": "使用条款"}, "privacy-policy": {"title": "隐私政策"}, "website": {"title": "官方网站"}, "copyright": {"title": "版权信息", "content": "© 2024 302.AI. 保留所有权利。"}, "license": {"title": "许可证", "content": "MIT License"}}, "assistant-settings": {"name": "助手设置"}, "general-settings": {"language": {"label": "语言"}, "name": "通用", "theme": {"dark": "深色", "label": "主题", "light": "浅色", "system": "系统"}, "privacy-mode": {"title": "隐私模式", "description": "自动继承"}, "version-update": {"label": "版本更新", "check-for-updates": "检查更新", "switch": {"label": "自动更新"}, "version-info": "版本信息", "update-available": "检测到新版本", "update-now": "立刻更新", "update-later": "稍后更新", "checking": "检查中", "no-update-available": "当前版本已经是最新版本", "check-failed": "检查失败，请重新尝试", "restart-to-update": "立即重启", "downloading": "下载中", "new-version-available": "新版本可用", "update-downloaded": "新版本已准备就绪，请重启应用进行安装", "new-version-downloaded": "新版本已就绪"}}, "icon-tooltip": "打开设置", "model-settings": {"model-list": {"label": "模型列表", "no-models-description": "模型列表为空...", "current": "当前", "collected": "收藏", "search-placeholder": "搜索...", "fetch-models": "获取模型", "add-model": "添加模型", "clear-models": "清空", "model-name": "模型名称", "model-capabilities": "模型能力", "model-type": "模型类型", "actions": "操作"}, "add-model-modal": {"title": "添加模型", "edit-title": "编辑模型", "model-id": {"label": "模型ID", "placeholder": "请输入模型ID", "description": "这是模型的真实名称，用于实际发起请求", "required-error": "模型ID不能为空"}, "description": {"label": "备注", "placeholder": "请输入备注", "description": "你可以为这个模型设置一个更容易分辨的名字，仅用于展示"}, "capabilities": {"label": "能力", "reasoning": "推理", "vision": "图像理解", "music": "音频理解", "video": "视频理解", "function_call": "工具使用"}, "type": {"label": "模型类型", "language": "语言", "image-generation": "图像生成", "tts": "TTS", "embedding": "Embedding", "rerank": "<PERSON><PERSON>"}, "actions": {"cancel": "取消", "save": "保存", "add-success": "添加成功", "edit-success": "编辑成功", "add-error-message": "添加失败", "edit-error-message": "编辑失败", "delete-error-message": "删除失败", "delete-success-message": "删除成功", "delete-title": "删除模型", "delete-description": "确定要删除该模型吗？", "delete-confirm-text": "删除", "clear-title": "清空所有模型", "clear-description": "确定要清空该提供商的所有模型吗？此操作无法撤销。", "clear-confirm-text": "清空所有", "clear-success-message": "所有模型已清空", "clear-error-message": "清空模型失败"}}, "model-provider": {"add-provider": "添加模型", "custom-provider": "自定义提供商", "add": "添加", "model-check-success": "模型校验成功", "model-check-failed": "模型校验失败", "select-provider": "选择Provider", "select-provider-description": " 请从左侧列表中选择一个Provider来配置其设置", "add-provider-form": {"check-key": "验证", "custom-provider": "自定义提供商", "placeholder": "请选择一个供应商...", "placeholder-2": "请输入API Key...", "placeholder-3": "请输入Base URL...", "provider-select": "选择供应商", "provider-type": "供应商类型", "placeholder-1": "请输入供应商名称...", "provider-name": "自定义名称", "default-name": "自定义供应商", "check-key-success": "API Key 验证成功！", "check-key-failed": "API Key 验证失败！", "checking": "验证中...", "unverified": "待验证", "verified": "已通过验证", "verification-failed": "未通过验证", "normalized-base-url": "规范化 Base URL", "full-api-endpoint": "完整 API 端点示例", "apply-normalized-url": "应用规范化 URL", "url-format-error": "URL 格式错误", "url-empty-error": "URL 不能为空", "url-invalid-format": "URL 格式无效", "verification-required": "请先验证 API Key 才可添加提供商", "verification-required-notice": "💡 提示：必须先通过 API 验证才能添加或保存提供商配置。", "verification-hint": "请点击验证按钮确认 API Key 可用性", "get-api-key": "点击此处获取API Key", "api-forward": "接口将会请求至", "interface-type": "接口类型", "interface-type-placeholder": "选择接口类型", "configure": "配置", "icon": "图标", "name": "名称", "name-placeholder": "请输入Provider名称"}, "delete": "删除", "description": "个模型", "not-configured": "尚未配置", "provider-error": "供应商错误", "edit": "编辑", "label": "模型提供商", "modal-action": {"add-provider": "添加供应商", "delete": "删除供应商", "delete-confirm": "删除", "delete-description": "请确认是否删除供应商", "delete-description-2": "该供应商提供的所有模型将被删除，", "delete-description-3": "同时配置了这些默认模型的助手将会被改为其他默认模型。", "edit": "配置", "add-provider-confirm": "添加"}, "no-provider-description": "暂无模型供应商", "edit-provider-form": {"check-key-success": "API Key 验证成功！", "check-key-failed": "API Key 验证失败！", "verification-required": "请先验证 API Key 才可保存提供商配置", "verification-required-notice": "💡 提示：必须先通过 API 验证才能添加或保存提供商配置。", "verification-hint": "请点击验证按钮确认 API Key 可用性"}, "star": "收藏"}, "name": "模型", "loading": "加载中..."}, "tab-title": "设置", "tool-settings": {"name": "工具设置"}, "shortcuts-settings": {"name": "快捷键", "title": "键盘快捷键", "actions": {"send-message": "发送消息", "new-chat": "新建会话", "clear-messages": "清空消息", "close-current-tab": "关闭当前标签页", "close-other-tabs": "关闭其他标签页", "delete-current-thread": "删除当前会话", "open-settings": "打开设置", "toggle-sidebar": "切换侧边栏", "quick-navigation": "快速导航", "command-palette": "命令面板", "stop-generation": "停止生成", "new-tab": "新建标签页", "new-session": "新建会话", "regenerate-response": "重新生成回复", "search": "搜索", "create-branch": "创建分支", "close-all-tabs": "关闭全部标签页", "restore-last-tab": "恢复上一个标签页", "screenshot": "截图", "next-tab": "下一个标签页", "previous-tab": "上一个标签页", "toggle-model-panel": "切换模型面板", "toggle-incognito-mode": "切换无痕模式", "branch-and-send": "创建分支并发送", "switch-to-tab-1": "切换到标签页1", "switch-to-tab-2": "切换到标签页2", "switch-to-tab-3": "切换到标签页3", "switch-to-tab-4": "切换到标签页4", "switch-to-tab-5": "切换到标签页5", "switch-to-tab-6": "切换到标签页6", "switch-to-tab-7": "切换到标签页7", "switch-to-tab-8": "切换到标签页8", "switch-to-tab-9": "切换到标签页9", "tab-switching-group": "快速标签页切换 (1-9)"}, "hints": {"send-message": "选择在聊天中发送消息的方式", "new-chat": "创建新对话的快捷键", "clear-messages": "仅清空当前会话的消息，模型设置等内容将会保留", "close-current-tab": "关闭当前活动的标签页", "close-other-tabs": "关闭除当前标签页外的所有标签页", "delete-current-thread": "删除当前对话线程", "open-settings": "打开设置页面", "toggle-sidebar": "显示或隐藏侧边栏", "quick-navigation": "快速导航到应用的不同部分", "command-palette": "打开命令面板进行快速操作", "stop-generation": "停止当前AI回复生成", "new-tab": "创建新的聊天标签页", "new-session": "在当前标签页开始新会话", "regenerate-response": "重新生成最后的AI回复", "search": "在应用内搜索", "create-branch": "从当前对话创建新分支", "close-all-tabs": "关闭所有打开的标签页", "restore-last-tab": "恢复最后关闭的标签页", "screenshot": "截图（全局快捷键）", "next-tab": "切换到下一个标签页", "previous-tab": "切换到上一个标签页", "toggle-model-panel": "显示或隐藏模型选择面板", "toggle-incognito-mode": "切换无痕模式进行私密对话", "branch-and-send": "同时创建分支并发送消息", "switch-to-tab-1": "切换到标签页1", "switch-to-tab-2": "切换到标签页2", "switch-to-tab-3": "切换到标签页3", "switch-to-tab-4": "切换到标签页4", "switch-to-tab-5": "切换到标签页5", "switch-to-tab-6": "切换到标签页6", "switch-to-tab-7": "切换到标签页7", "switch-to-tab-8": "切换到标签页8", "switch-to-tab-9": "切换到标签页9", "tab-switching-group": "快速切换到指定标签页"}, "description": {"title": "键盘快捷键说明", "content": "您可以自定义各种操作的键盘快捷键。某些操作仅在使用 302.AI 提供商模型时可用。"}, "recorder": {"placeholder": "点击设置快捷键", "press-keys": "按下按键...", "cancel": "取消", "reset": "重置", "clear": "清空", "no-shortcut": "无快捷键", "error": {"modifier-required": "快捷键必须包含至少一个辅助按键（Ctrl/Cmd/Alt/Shift）", "shortcut-conflict": "该快捷键已存在，请重新录制"}}, "scope": {"label": "作用域", "app": "应用内", "global": "全局"}}, "text": "设置", "preference-settings": {"name": "偏好", "search-provider": {"label": "默认搜索服务"}, "display-app-store": {"label": "302.AI 应用超市", "switch": {"label": "在首页显示应用超市"}}, "stream-output": {"label": "流式输出", "smoother": {"label": "流输出平滑", "switch": {"label": "启用流式输出平滑功能"}}, "speed": {"label": "输出速度", "slow": "慢速", "normal": "正常", "fast": "快速"}}, "collapse-code-block": {"label": "聊天设置", "switch": {"hide-code": "自动折叠代码块", "hide-reason": "隐藏推理过程", "collapse-think": "自动折叠推理过程", "disable-markdown": "关闭markdown渲染"}}, "model-select": {"label": "新会话模型", "placeholder": "选择模型", "search-placeholder": "搜索模型...", "no-models-found": "未找到模型", "use-last-model": "上一次会话使用"}}}, "sidebar": {"close-sidebar": {"tooltip": "关闭边栏"}, "menu-item": {"clean-messages": "清空消息", "collect-thread": "收藏", "generate-title": "生成标题", "delete": "删除", "rename": "重命名", "uncollect-thread": "取消收藏", "delete-all": "删除所有"}, "new-thread": {"tooltip": "新聊天"}, "open-sidebar": {"tooltip": "打开边栏"}, "search": {"placeholder": "搜索..."}, "search-thread": {"tooltip": "搜索会话", "placeholder": "搜索"}, "section": {"collected": "收藏", "earlier": "更早", "last30Days": "过去30天", "last7Days": "过去7天", "today": "今天", "yesterday": "昨天", "justNow": "刚刚"}}, "tab-bar": {"menu-item": {"close": "关闭", "close-all": "关闭全部"}}, "thread": {"new-thread-title": "新会话", "create-thread-error": "抱歉，新会话创建失败！", "lack-model": "请先选择一个模型！", "lack-provider": "缺少模型提供商！", "selected-model-not-found": "请先选择一个模型！", "provider-not-found-for-selected-model": "模型提供商设置错误！", "message-not-found": "此消息不存在！", "failed-to-generate-ai-response": "生成AI回复失败！", "private-thread-title": "私密聊天"}, "privacy-mode": {"enable-tooltip": "启用隐私模式", "disable-tooltip": "禁用隐私模式", "cannot-toggle-tooltip": "会话开始后无法切换隐私模式", "cannot-toggle": {"title": "无法切换隐私模式", "description": "隐私模式只能在会话开始前更改"}, "enabled": {"title": "隐私模式已启用", "description": "此会话不会被保存或同步"}, "disabled": {"title": "隐私模式已禁用", "description": "此会话将正常保存"}, "error": {"title": "隐私模式错误", "description": "切换隐私模式失败"}, "confirm-switch": {"message": "您即将{{action}}。这将丢弃您的私密会话内容。是否继续？"}, "confirm-dialog": {"title": "确认隐私模式操作", "cancel": "取消", "confirm": "继续"}}, "thread-menu": {"actions": {"cancel": "取消", "clean-messages": {"confirmText": "清空", "description": "清空消息会删除会话下所有消息和文件，确定要继续吗？", "title": "清空会话消息"}, "confirm": "确定", "delete": {"confirmText": "删除", "description": "会话删除后无法恢复，请谨慎操作。", "title": "确定要删除这个会话吗？"}, "rename": {"confirmText": "确定", "description": "请输入新的会话名称。", "edit": {"placeholder": "新的会话名称..."}, "title": "重命名会话"}, "delete-all": {"title": "确定要删除所有的会话吗？", "description": "除已收藏的会话外，其他所有会话将会被删除并且无法恢复，请谨慎操作。", "confirmText": "删除所有"}}}, "message": {"copy": "复制", "copy-success": "复制成功", "copy-failed": "复制失败", "refresh": "重新生成", "edit": "编辑", "delete": "删除", "thinking": "AI正在思考", "edit-dialog": {"title": "编辑消息", "save": "保存", "cancel": "取消", "save-success": "消息更新成功", "save-error": "消息更新失败"}, "generate-failed": "生成失败，请重新尝试...", "delete-success": "消息删除成功", "delete-error": "消息删除失败", "context-menu": {"create-new-branch": "从此处创建分支", "create-new-branch-error": "抱歉，创建分支失败！", "new-thread-title": "新会话", "copy-selected": "复制选中的内容"}, "think": "思考过程", "outputing": "输出中...", "reason": "推理过程", "completed": "已完成"}, "common": {"copied-success": "复制成功", "copied-failed": "复制失败", "copy-to-clipboard": "复制"}, "message-list": {"markdown": {"generating-diagram": "正在生成图表...", "waiting-for-diagram": "等待图表内容...", "diagram-syntax-error": "图表语法错误"}}, "stop-generating": "停止", "new-thread": {"hello-world": "嗨，准备好和我一起探索世界了吗?", "toolbox-label": "AI 应用", "toolbox-button": "更多应用", "toolbox-title": "AI 工具", "toolbox-search-placeholder": "搜索"}, "attachment": {"file-too-large": "文件大小不能超过10MB"}, "preview": {"zoom-in": "放大", "zoom-out": "缩小", "rotate-left": "向左旋转", "rotate-right": "向右旋转", "reset-zoom": "重置缩放", "download": "下载", "copy-code": "复制代码", "copy-text": "复制文本", "copy-content": "复制内容", "toggle-line-numbers": "切换行号显示", "open-external": "使用外部程序打开", "loading": "加载中...", "loading-code": "加载代码中...", "loading-document": "加载文档中...", "loading-audio": "加载音频中...", "loading-image": "加载图片中...", "loading-text": "加载文本中...", "copy-failed": "复制失败", "copied-to-clipboard": "已复制到剪贴板", "content-copied": "内容已复制", "failed-to-load": "加载失败", "failed-to-decode": "解码失败", "no-content-available": "无可用内容", "document-parsing-unavailable": "文档解析不可用。请使用外部查看器。", "file-preview-not-supported": "此文件类型无法在应用内预览。", "close": "关闭", "text-preview": "文本预览", "code-preview": "代码预览", "text-file": "文本文件", "code-file": "代码文件", "help-text": {"image": "使用鼠标滚轮缩放 • 拖拽平移 • R 键旋转 • Esc 键关闭", "audio": "空格键播放/暂停 • ← → 键跳转 • M 键静音 • Esc 键关闭", "code": "Ctrl+C 复制 • L 键切换行号 • + / - 键缩放 • Esc 键关闭", "document": "+ / - 键缩放 • 点击外部链接在系统查看器中打开 • Esc 键关闭", "text": "Ctrl+C 复制 • + / - 键缩放 • Esc 键关闭"}}, "artifacts": {"preview": "预览", "code": "代码", "lines": "行", "collapse": "折叠", "expand": "展开"}, "shortcuts": {"tab-not-exist": "标签页 {{tabNumber}} 不存在", "tab-switch-error": "切换到标签页 {{tabNumber}} 失败"}, "toolbox-error-msg": "302.AI供应商的API Key未配置或不正确，请重新配置"}